### ValueAddedFileController HTTP 测试文件
### 增值交付单文件管理接口测试

### 1. 正常上传交付单文件测试
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 2. 缺少交付单编号测试
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 3. 空交付单编号测试
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 4. 缺少文件测试
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 5. 使用简化的表单数据格式测试（推荐用于IDE测试）
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data

deliveryOrderNo=DO20250818002&file=< ./2025-07-29T230358.200.xlsx

### 6. 测试不同的交付单编号格式
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data

deliveryOrderNo=TEST-ORDER-123&file=< ./2025-07-29T230358.200.xlsx

### 7. 测试长交付单编号
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data

deliveryOrderNo=VERY_LONG_DELIVERY_ORDER_NUMBER_FOR_TESTING_PURPOSES_2025&file=< ./2025-07-29T230358.200.xlsx

### 8. 测试包含特殊字符的交付单编号
POST http://localhost:8080/bxmCustomer/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data

deliveryOrderNo=DO-2025/08/18-001&file=< ./2025-07-29T230358.200.xlsx

###
### 测试说明：
### 1. 第1个测试用例是正常的文件上传，应该返回成功结果和文件ID
### 2. 第2个测试用例缺少交付单编号，应该返回"交付单编号不能为空"错误
### 3. 第3个测试用例交付单编号为空，应该返回"交付单编号不能为空"错误
### 4. 第4个测试用例缺少文件，应该返回"文件不能为空"错误
### 5. 第5-8个测试用例使用简化格式测试不同的交付单编号场景
###
### 预期响应格式：
### 成功: {"code": 200, "msg": "文件上传成功", "data": 文件ID}
### 失败: {"code": 500, "msg": "错误信息", "data": null}
###
### 注意事项：
### - 确保测试文件 2025-07-29T230358.200.xlsx 存在于当前目录
### - 服务器需要运行在 localhost:8080
### - 如果使用不同的服务器地址，请修改 URL
### - 测试前请确保相关服务已启动
